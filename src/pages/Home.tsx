import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import cover2Icon from '@/assets/images/cover2.png';
import { Button } from 'antd';

// 音乐分类数据
const musicCategories = [
  {
    id: 'pop',
    name: 'Pop',
    tracks: [
      {
        id: 1,
        title: 'Midnight City Lights',
        artist: 'Beats by <PERSON><PERSON>',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 2,
        title: 'Cultural Music Exchange',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 3,
        title: 'Electronic',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 4,
        title: 'Music In Everyday Life',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 5,
        title: 'MONTERO (Call Me by Your Name)',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
  {
    id: 'rap',
    name: 'Rap',
    tracks: [
      {
        id: 6,
        title: 'Midnight City',
        artist: 'Beats by <PERSON><PERSON>',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 7,
        title: '创建音乐作品集',
        artist: '今夜天堂重新开启',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 8,
        title: 'Super Freaky Girl',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 9,
        title: "I Ain't Worried",
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 10,
        title: 'She Loves You',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 11,
        title: 'As It Was',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
  {
    id: 'bass-music',
    name: 'Bass Music',
    tracks: [
      {
        id: 12,
        title: 'Midnight City Lights',
        artist: 'Beats by Juniper',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 13,
        title: 'Super Freaky Girl',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 14,
        title: "I Ain't Worried",
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 15,
        title: 'She Loves You',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 16,
        title: 'As It Was',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
  {
    id: 'jazz',
    name: 'Jazz',
    tracks: [
      {
        id: 17,
        title: 'Midnight City Lights',
        artist: 'Beats by Juniper',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 18,
        title: 'Quevedo: Bzrp Music Sessions, Vol. 52',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 19,
        title: "I'm Good (Blue)",
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 20,
        title: "I'm Good (Blue)",
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 21,
        title: 'As It Was',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
  {
    id: 'ambient',
    name: 'Ambient',
    tracks: [
      {
        id: 22,
        title: 'Midnight City Lights',
        artist: 'Beats by Juniper',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 23,
        title: 'I Like You',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 24,
        title: "I Ain't Worried",
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 25,
        title: 'Quevedo: Bzrp Music Sessions, Vol. 52',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 26,
        title: 'As It Was',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
  {
    id: 'noise',
    name: 'Noise',
    tracks: [
      {
        id: 27,
        title: 'Midnight City Lights',
        artist: 'Beats by Juniper',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 28,
        title: "I Ain't Worried",
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 29,
        title: 'Super Freaky Girl',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 30,
        title: 'Shut Down',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 31,
        title: 'She Loves You',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
  {
    id: 'jungle',
    name: 'Jungle',
    tracks: [
      {
        id: 32,
        title: 'Midnight City Lights',
        artist: 'Beats by Juniper',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 33,
        title: "I Ain't Worried",
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 34,
        title: "I'm Good (Blue)",
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 35,
        title: 'Shut Down',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 36,
        title: 'About Damn',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
  {
    id: 'ambient2',
    name: 'Ambient',
    tracks: [
      {
        id: 37,
        title: 'Midnight City Lights',
        artist: 'Beats by Juniper',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 38,
        title: 'I Like You',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 39,
        title: "I Ain't Worried",
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 40,
        title: 'Quevedo: Bzrp Music Sessions, Vol. 52',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 41,
        title: 'About Damn',
        artist: '',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 42,
        title: "I'm Good (Blue)",
        artist: '',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
];

// 图表数据
const weeklyRevenueData = [
  {
    title: 'Quevedo: Bzrp Music Sessions, Vo...',
    genre: 'Pop',
    revenue: '19024.30',
  },
  { title: "I'm Good (Blue)", genre: 'Jazz', revenue: '15978.80' },
  {
    title: '月夜舞·光影中的我们音乐体验',
    genre: 'Bass Music',
    revenue: '10103.00',
  },
  {
    title: 'MONTERO (Call Me by Your Name)',
    genre: 'Ambient',
    revenue: '8124.31',
  },
  { title: 'Cultural Music Exchange', genre: 'Jungle', revenue: '7009.00' },
  {
    title: 'MONTERO (Call Me by Your Name)',
    genre: 'Noise',
    revenue: '5000.11',
  },
  {
    title: 'Quevedo: Bzrp Music Sessions, Vo...',
    genre: 'Pop',
    revenue: '3002.21',
  },
];

const weeklyStreamingData = [
  {
    title: 'Quevedo: Bzrp Music Sessions, Vo...',
    genre: 'Pop',
    revenue: '19024.30',
  },
  { title: "I'm Good (Blue)", genre: 'Jazz', revenue: '15978.80' },
  {
    title: '月夜舞·光影中的我们音乐体验',
    genre: 'Bass Music',
    revenue: '10103.00',
  },
  {
    title: 'MONTERO (Call Me by Your Name)',
    genre: 'Ambient',
    revenue: '8124.31',
  },
  { title: 'Cultural Music Exchange', genre: 'Jungle', revenue: '7009.00' },
  {
    title: 'MONTERO (Call Me by Your Name)',
    genre: 'Noise',
    revenue: '5000.11',
  },
  {
    title: 'Quevedo: Bzrp Music Sessions, Vo...',
    genre: 'Pop',
    revenue: '3002.21',
  },
];

const Home: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="flex bg-[#0d0d0d]">
      {/* 主内容区域 */}
      <div className="flex-1 p-8">
        {/* 音乐分类网格 */}
        <div className="grid grid-cols-4 gap-8">
          {musicCategories.map(category => (
            <div
              key={category.id}
              className="space-y-4 bg-gray-800/60 backdrop-blur-md rounded-20px p-4 "
            >
              {/* 分类标题 */}
              <div className="flex items-center justify-between">
                <h2 className="text-white text-20px font-bold">
                  {category.name}
                </h2>
                <Button type="text">See all</Button>
              </div>
              <div className="flex gap-4">
                <img
                  src={cover2Icon}
                  alt="cover"
                  className="w-105px h-105px rounded-10px"
                />
                <div className="flex flex-col gap-2">
                  <div className="text-white text-16px font-bold">
                    Midnight City Lights
                  </div>
                  <div className="text-label">Beats by Juniper</div>
                </div>
              </div>

              {/* 音乐列表 */}
              <div className="space-y-3">
                {category.tracks.map(track => (
                  <div
                    key={track.id}
                    className="flex items-center space-x-3 group cursor-pointer"
                  >
                    {/* 专辑封面 */}
                    {/* <div className="w-16 h-16 bg-[#333] rounded-lg overflow-hidden flex-shrink-0 group-hover:opacity-80 transition-opacity">
                      <img
                        src={coverIcon}
                        alt={track.title}
                        className="w-full h-full object-cover"
                        onError={e => {
                          (e.target as HTMLImageElement).src =
                            'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjMyIiB5PSIzMiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9ImNlbnRyYWwiIGZpbGw9IiM2NjYiIGZvbnQtc2l6ZT0iMTIiIGZvbnQtZmFtaWx5PSJBcmlhbCI+4pmqPC90ZXh0Pgo8L3N2Zz4=';
                        }}
                      />
                    </div> */}

                    {/* 音乐信息 */}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-white text-14px font-medium truncate group-hover:text-[#ff6b35] transition-colors">
                        {track.title}
                      </h3>
                      {track.artist && (
                        <p className="text-[#999] text-12px truncate mt-1">
                          {track.artist}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 右侧边栏 */}
      <div className="w-80 bg-[#0d0d0d] p-6 space-y-8">
        {/* Weekly Revenue Chart */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-white text-16px font-semibold">
              Weekly Revenue Chart
            </h3>
            <button className="text-[#ff6b35] text-12px hover:underline">
              See all
            </button>
          </div>

          <div className="space-y-3">
            {weeklyRevenueData.map((item, index) => (
              <div
                key={index}
                className="flex items-center justify-between py-2"
              >
                <div className="flex-1 min-w-0 pr-4">
                  <div className="text-white text-12px font-medium truncate">
                    {item.title}
                  </div>
                  <div className="text-[#999] text-10px mt-1">{item.genre}</div>
                </div>
                <div className="text-white text-12px font-semibold">
                  {item.revenue}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Weekly Streaming Chart */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-white text-16px font-semibold">
              Weekly Streaming Chart
            </h3>
            <button className="text-[#ff6b35] text-12px hover:underline">
              See all
            </button>
          </div>

          <div className="space-y-3">
            {weeklyStreamingData.map((item, index) => (
              <div
                key={index}
                className="flex items-center justify-between py-2"
              >
                <div className="flex-1 min-w-0 pr-4">
                  <div className="text-white text-12px font-medium truncate">
                    {item.title}
                  </div>
                  <div className="text-[#999] text-10px mt-1">{item.genre}</div>
                </div>
                <div className="text-white text-12px font-semibold">
                  {item.revenue}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
