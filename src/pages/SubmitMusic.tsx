import React, { useState } from 'react';
// import './SubmitMusic.css';
import {
  Form,
  Input,
  Button,
  Upload,
  DatePicker,
  Select,
  Checkbox,
  Row,
  Col,
} from 'antd';
import {
  UploadOutlined,
  PlusOutlined,
  CalendarOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { useLanguage } from '@/hooks/useLanguage';

const { TextArea } = Input;
const { Option } = Select;

interface SubmitMusicFormData {
  primaryArtist: string;
  artistBio: string;
  coverArt: any;
  trackTitle: string;
  labelName: string;
  primaryLanguage: string;
  bidStartDate: any;
  bidEndDate: any;
  copyrightYear: any;
  copyrightName: string;
  publishingYear: any;
  publishingName: string;
  enableiTunesPreOrder: boolean;
  liveConcertRecording: boolean;
  remasteredRecording: boolean;
  supportsDolbyAtmos: boolean;
  additionalFiles: any[];
}

const SubmitMusic: React.FC = () => {
  const { t } = useLanguage();
  const [form] = Form.useForm<SubmitMusicFormData>();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: SubmitMusicFormData) => {
    setLoading(true);
    try {
      console.log('提交的数据:', values);
      // TODO: 调用API提交数据
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
  };

  const beforeUpload = () => {
    return false;
  };

  return (
    <div className="min-h-screen bg-[#0d0d0d] relative">
      <div className="pl-8 pr-8 pt-6">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="max-w-none"
        >
          {/* Artist Info Section */}
          <div className="mb-10">
            <h2 className="text-white text-[22px] font-bold mb-6 font-arial">
              Artist Info
            </h2>

            <div className="mb-6">
              <div className="text-[#656565] text-[18px] font-bold mb-5 font-arial">
                Primary Artist (Stage Name):
              </div>
              <div className="w-[854px]">
                <Input
                  className="h-[42px] bg-[#282828] border-none text-[#999999] text-[14px] px-4"
                  placeholder="value25"
                  style={{ borderRadius: 0 }}
                />
              </div>
            </div>

            <div className="mb-6">
              <div className="text-[#656565] text-[18px] font-bold mb-5 font-arial">
                Artist Bio (0 to 400 characters):
              </div>
              <TextArea
                className="bg-[#282828] border-none text-[#999999] text-[14px] leading-[27px] px-4 py-3"
                style={{
                  borderRadius: 0,
                  height: '139px',
                  resize: 'none',
                  width: '1462px',
                }}
                placeholder='Koji Takanashi, born on April 13, 1963 in Tokyo, Japan, is a Japanese composer, arranger, keyboard player, and a member of the Japanese hard rock band "Musashi". He is currently mainly engaged in the production of animation music. The classic tracks in a series of well-known animations and TV works such as "Naruto", "Pretty Cure", "Fairy Tail", "Hell Girl", "Super Star God", and "Log Horizon" are all from his hand.'
                maxLength={400}
              />
            </div>
          </div>

          {/* Track Info Section */}
          <div className="mb-10">
            <h2 className="text-white text-[22px] font-bold mb-8 font-arial">
              Track Info
            </h2>

            <div className="flex gap-8 mb-8">
              {/* Cover Art Upload */}
              <div className="w-[340px] h-[340px]">
                <div
                  className="!w-[340px] !h-[340px] flex flex-col items-center justify-center cursor-pointer relative"
                  style={{
                    background:
                      'linear-gradient(rgba(0,0,0,0.8), rgba(0,0,0,0.8)), url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzQwIiBoZWlnaHQ9IjM0MCIgdmlld0JveD0iMCAwIDM0MCAzNDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzNDAiIGhlaWdodD0iMzQwIiBmaWxsPSIjMjgyODI4Ii8+Cjwvc3ZnPgo=")',
                    borderRadius: '6px',
                  }}
                >
                  <Upload
                    name="coverArt"
                    listType="picture-card"
                    className="cover-art-uploader"
                    beforeUpload={beforeUpload}
                    showUploadList={false}
                  >
                    <div className="flex flex-col items-center justify-center">
                      <UploadOutlined className="text-[64px] text-[#656565] mb-4" />
                      <div className="text-[#656565] text-[14px] font-medium text-center w-[175px]">
                        CoverArt+Upload
                      </div>
                    </div>
                  </Upload>
                </div>
              </div>

              {/* Track Details */}
              <div className="flex-1">
                <div className="flex gap-4 mb-4">
                  <div className="flex-1">
                    <div className="text-[#656565] text-[14px] mb-2">
                      Primary Artist (Stage Name):
                    </div>
                    <Input
                      className="h-[42px] bg-[#282828] border-none text-[#999999] text-[14px] px-4"
                      placeholder="Super Freaky Girl"
                      style={{ borderRadius: 0 }}
                    />
                  </div>
                  <div className="flex-1">
                    <div className="text-[#656565] text-[14px] mb-2">
                      Label Name:
                    </div>
                    <Input
                      className="h-[42px] bg-[#282828] border-none text-[#999999] text-[14px] px-4"
                      placeholder="Super Freaky Girl"
                      style={{ borderRadius: 0 }}
                    />
                  </div>
                </div>

                {/* Artist Bio Display */}
                <div className="mb-4">
                  <div className="text-[#656565] text-[14px] mb-2">
                    Artist Bio (0 to 400 characters):
                  </div>
                  <div
                    className="bg-[#999999] p-4 text-[#000000] text-[14px] leading-normal"
                    style={{ height: '139px' }}
                  >
                    Koji Takanashi, born on April 13, 1963 in Tokyo, Japan, is a
                    Japanese composer, arranger, keyboard player, and a member
                    of the Japanese hard rock band "Musashi". He is currently
                    mainly engaged in the production of animation music. The
                    classic tracks in a series of well-known animations and TV
                    works such as "Naruto", "Pretty Cure", "Fairy Tail", "Hell
                    Girl", "Super Star God", and "Log Horizon" are all from his
                    hand.
                  </div>
                </div>

                {/* Upload Button */}
                <div className="flex items-center">
                  <UploadOutlined className="text-[24px] text-[#656565] mr-2" />
                  <span className="text-[#656565] text-[14px] font-medium">
                    Upload
                  </span>
                </div>
              </div>
            </div>

            {/* Form Fields Row 1 */}
            <div className="flex gap-8 mb-6">
              <div className="w-[454px]">
                <div className="text-[#656565] text-[18px] font-bold mb-5 font-arial">
                  Primary Artist (Stage Name):
                </div>
                <Input
                  className="h-[42px] bg-[#282828] border-none text-[#999999] px-5"
                  style={{ borderRadius: 0 }}
                />
              </div>
              <div className="w-[454px]">
                <div className="text-[#656565] text-[18px] font-bold mb-5 font-arial">
                  Primary Language:
                </div>
                <Select
                  className="w-full h-[42px] custom-select"
                  placeholder="Select language"
                  style={{ borderRadius: 0 }}
                  suffixIcon={
                    <div className="w-[9.78px] h-[9.78px] bg-[#656565] rotate-45 transform border border-[#656565]"></div>
                  }
                >
                  <Option value="en">English</Option>
                  <Option value="zh">中文</Option>
                  <Option value="ja">日本語</Option>
                </Select>
              </div>
              <div className="w-[454px]">
                <div className="text-[#656565] text-[18px] font-bold mb-5 font-arial">
                  Primary Artist (Stage Name):
                </div>
                <Input
                  className="h-[42px] bg-[#282828] border-none text-[#999999] px-5"
                  style={{ borderRadius: 0 }}
                />
              </div>
            </div>

            {/* Form Fields Row 2 */}
            <div className="flex gap-8 mb-8">
              <div className="w-[454px]">
                <div className="text-[#656565] text-[18px] font-bold mb-5 font-arial">
                  Primary Language:
                </div>
                <Input
                  className="h-[42px] bg-[#282828] border-none text-[#999999] px-5"
                  style={{ borderRadius: 0 }}
                />
              </div>
              <div className="w-[454px]">
                <div className="text-[#656565] text-[18px] font-bold mb-5 font-arial">
                  Primary Language:
                </div>
                <Select
                  className="w-full h-[42px] custom-select"
                  placeholder="Select language"
                  style={{ borderRadius: 0 }}
                  suffixIcon={
                    <div className="w-[9.78px] h-[9.78px] bg-[#656565] rotate-45 transform border border-[#656565]"></div>
                  }
                >
                  <Option value="en">English</Option>
                  <Option value="zh">中文</Option>
                  <Option value="ja">日本語</Option>
                </Select>
              </div>
              <div className="w-[454px]">
                <div className="text-[#656565] text-[18px] font-bold mb-5 font-arial">
                  Primary Language:
                </div>
                <Select
                  className="w-full h-[42px] custom-select"
                  placeholder="Select language"
                  style={{ borderRadius: 0 }}
                  suffixIcon={
                    <div className="w-[9.78px] h-[9.78px] bg-[#656565] rotate-45 transform border border-[#656565]"></div>
                  }
                >
                  <Option value="en">English</Option>
                  <Option value="zh">中文</Option>
                  <Option value="ja">日本語</Option>
                </Select>
              </div>
            </div>

            {/* Bid Date Range */}
            <div className="mb-8">
              <div className="text-[#656565] text-[18px] font-bold mb-5 font-arial">
                Bids Between:
              </div>
              <div className="flex items-center gap-8">
                <div className="relative w-[454px]">
                  <DatePicker
                    className="w-full h-[42px] bg-[#282828] border-none custom-datepicker"
                    placeholder="25/11/2022"
                    style={{ borderRadius: '8px' }}
                    suffixIcon={
                      <CloseOutlined className="text-[18px] text-[#656565]" />
                    }
                  />
                  <CalendarOutlined className="absolute left-4 top-1/2 transform -translate-y-1/2 text-[16px] text-[#656565]" />
                </div>
                <div className="relative w-[454px]">
                  <DatePicker
                    className="w-full h-[42px] bg-[#282828] border-none custom-datepicker"
                    placeholder="25/11/2022"
                    style={{ borderRadius: '8px' }}
                  />
                  <CalendarOutlined className="absolute left-4 top-1/2 transform -translate-y-1/2 text-[16px] text-[#656565]" />
                </div>
              </div>
            </div>
          </div>

          {/* Copyright Info Section */}
          <div className="mb-10">
            <h2 className="text-white text-[22px] font-bold mb-8 font-arial">
              Copyright Info
            </h2>

            <div className="flex gap-8 mb-6">
              <div className="w-[454px]">
                <div className="text-[#656565] text-[18px] font-bold mb-5 font-arial">
                  Year:
                </div>
                <div className="relative">
                  <DatePicker
                    className="w-full h-[42px] bg-[#282828] border-none custom-datepicker"
                    placeholder="25/11/2022"
                    style={{ borderRadius: '8px' }}
                    suffixIcon={
                      <CloseOutlined className="text-[18px] text-[#656565]" />
                    }
                  />
                  <CalendarOutlined className="absolute left-4 top-1/2 transform -translate-y-1/2 text-[16px] text-[#656565]" />
                </div>
              </div>
              <div className="w-[454px]">
                <div className="text-[#656565] text-[18px] font-bold mb-5 font-arial">
                  Name
                </div>
                <Input
                  className="h-[42px] bg-[#282828] border-none text-[#999999] px-5"
                  style={{ borderRadius: 0 }}
                />
              </div>
            </div>

            <div className="flex gap-8 mb-8">
              <div className="w-[454px]">
                <div className="text-[#656565] text-[18px] font-bold mb-5 font-arial">
                  Year:
                </div>
                <div className="relative">
                  <DatePicker
                    className="w-full h-[42px] bg-[#282828] border-none custom-datepicker"
                    placeholder="25/11/2022"
                    style={{ borderRadius: '8px' }}
                    suffixIcon={
                      <CloseOutlined className="text-[18px] text-[#656565]" />
                    }
                  />
                  <CalendarOutlined className="absolute left-4 top-1/2 transform -translate-y-1/2 text-[16px] text-[#656565]" />
                </div>
              </div>
              <div className="w-[454px]">
                <div className="text-[#656565] text-[18px] font-bold mb-5 font-arial">
                  Name
                </div>
                <Input
                  className="h-[42px] bg-[#282828] border-none text-[#999999] px-5"
                  style={{ borderRadius: 0 }}
                />
              </div>
            </div>

            {/* Checkboxes */}
            <div className="space-y-4">
              <div className="flex items-center">
                <div className="w-[15px] h-[15px] border border-[#656565] mr-4 flex items-center justify-center">
                  <div className="w-[7px] h-[7px] bg-transparent"></div>
                </div>
                <span className="text-[#656565] text-[18px] font-bold font-arial">
                  Supports Dolby Atmos
                </span>
              </div>
              <div className="flex items-center">
                <div className="w-[15px] h-[15px] border border-[#656565] mr-4 flex items-center justify-center">
                  <div className="w-[7px] h-[7px] bg-transparent"></div>
                </div>
                <span className="text-[#656565] text-[18px] font-bold font-arial">
                  Supports Dolby Atmos
                </span>
              </div>
            </div>
          </div>

          {/* Two Column Section */}
          <div className="flex gap-16 mb-10">
            {/* Left Column - Copyright Info (duplicate) */}
            <div className="flex-1">
              <h2 className="text-white text-[22px] font-bold mb-8 font-arial">
                Copyright Info
              </h2>
            </div>

            {/* Right Column - Release Options */}
            <div className="flex-1">
              <h2 className="text-white text-[22px] font-bold mb-8 font-arial">
                Release Options (optional):
              </h2>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-[15px] h-[15px] border border-[#656565] mr-4 flex items-center justify-center">
                    <div className="w-[7px] h-[7px] bg-transparent"></div>
                  </div>
                  <span className="text-[#656565] text-[18px] font-bold font-arial">
                    Enable iTunes Pre-Order
                  </span>
                </div>
                <div className="flex items-center">
                  <div className="w-[15px] h-[15px] border border-[#656565] mr-4 flex items-center justify-center">
                    <div className="w-[7px] h-[7px] bg-transparent"></div>
                  </div>
                  <span className="text-[#656565] text-[18px] font-bold font-arial">
                    Live Concert Recording
                  </span>
                </div>
                <div className="flex items-center">
                  <div className="w-[15px] h-[15px] border border-[#656565] mr-4 flex items-center justify-center">
                    <div className="w-[7px] h-[7px] bg-transparent"></div>
                  </div>
                  <span className="text-[#656565] text-[18px] font-bold font-arial">
                    Remastered Recording
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Media Files Section */}
          <div className="mb-16">
            <h2 className="text-white text-[22px] font-bold mb-8 font-arial">
              Additional Media Files (optional)
            </h2>

            <Upload
              name="additionalFiles"
              multiple
              beforeUpload={beforeUpload}
              className="additional-files-uploader"
            >
              <div className="w-[218px] h-[218px] bg-[#282828] flex flex-col items-center justify-center cursor-pointer">
                <div className="w-16 h-16 relative mb-4">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-16 h-1 bg-[#999999] rounded-[11px]"></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center rotate-90">
                    <div className="w-16 h-1 bg-[#999999] rounded-[11px]"></div>
                  </div>
                </div>
                <div className="text-[#656565] text-[14px] font-medium">
                  Upload
                </div>
              </div>
            </Upload>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-center gap-6 mb-60px">
            <Button
              size="large"
              className="w-[496px] h-[63px] bg-transparent text-[#ff5e13] text-[16px] font-bold border border-[#ff5e13] hover:bg-[#ff5e13] hover:text-black transition-colors"
              style={{ borderRadius: '6px' }}
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              size="large"
              htmlType="submit"
              loading={loading}
              className="w-[496px] h-[63px] bg-[#ff5e13] text-black text-[16px] font-bold border-none hover:bg-[#e5541a] transition-colors"
              style={{ borderRadius: '6px' }}
            >
              Confirm
            </Button>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default SubmitMusic;
