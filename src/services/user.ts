import { ApiService } from './request';
import type {
  UserProfileResponse,
  UpdateProfileRequest,
  ChangePasswordRequest,
  ChangeEmailRequest,
  SendOtpRequest,
  BooleanResponse,
  ApiResponse,
  AvatarPresignedUrlResponse,
} from '@/types/api';
import { API_ENDPOINTS } from '@/types/api';
import type { LoginFormData, RegisterFormData } from '@/types';

/**
 * 用户相关 API 服务
 */
export const userApi = {
  /**
   * 获取当前用户资料
   * @returns 用户资料响应
   */
  async getProfile(): Promise<ApiResponse<UserProfileResponse>> {
    return ApiService.get(API_ENDPOINTS.USER.PROFILE);
  },

  /**
   * 更新当前用户资料
   * @param params 更新参数
   * @returns 布尔响应
   */
  async updateProfile(
    params: UpdateProfileRequest
  ): Promise<ApiResponse<BooleanResponse>> {
    return ApiService.put(API_ENDPOINTS.USER.PROFILE, params);
  },

  /**
   * 修改密码
   * @param params 修改密码参数
   * @returns 布尔响应
   */
  async changePassword(
    params: ChangePasswordRequest
  ): Promise<ApiResponse<BooleanResponse>> {
    return ApiService.put(API_ENDPOINTS.USER.CHANGE_PASSWORD, params);
  },

  /**
   * 修改邮箱
   * @param params 修改邮箱参数
   * @returns 布尔响应
   */
  async changeEmail(
    params: ChangeEmailRequest
  ): Promise<ApiResponse<BooleanResponse>> {
    return ApiService.put(API_ENDPOINTS.USER.CHANGE_EMAIL, params);
  },

  /**
   * 获取头像上传预签名URL
   * @param filename 文件名
   * @returns 预签名URL响应
   */
  async getAvatarPresignedUrl(
    filename: string
  ): Promise<ApiResponse<AvatarPresignedUrlResponse>> {
    return ApiService.get(
      `${API_ENDPOINTS.META.AVATAR_PRESIGNED_URL}?filename=${encodeURIComponent(filename)}`
    );
  },

  /**
   * 上传头像到S3
   * @param presignedUrl 预签名URL
   * @param file 头像文件
   * @returns 上传结果
   */
  async uploadAvatarToS3(presignedUrl: string, file: File): Promise<void> {
    const response = await fetch(presignedUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type,
      },
    });

    if (!response.ok) {
      throw new Error(`上传失败: ${response.status} ${response.statusText}`);
    }
  },

  /**
   * 上传头像（完整流程）
   * @param file 头像文件
   * @returns 头像 URL
   */
  async uploadAvatar(file: File): Promise<{ url: string }> {
    // 1. 获取预签名URL
    const presignedResponse = await this.getAvatarPresignedUrl(file.name);
    if (presignedResponse.code !== 200) {
      throw new Error(presignedResponse.message || '获取上传URL失败');
    }

    const { presignedUrl } = presignedResponse.body;

    // 2. 上传文件到S3
    await this.uploadAvatarToS3(presignedUrl, file);

    // 3. 提取文件URL（去掉查询参数）
    const fileUrl = presignedUrl.split('?')[0];

    return { url: fileUrl };
  },

  /**
   * 发送修改邮箱验证码
   * @param params 发送验证码参数
   * @returns 布尔响应
   */
  async sendChangeEmailOtp(
    params: SendOtpRequest
  ): Promise<ApiResponse<BooleanResponse>> {
    return ApiService.post(API_ENDPOINTS.AUTH.SEND_CHANGE_USERNAME_OTP, params);
  },
};

/**
 * 用户工具函数
 */
export const userUtils = {
  /**
   * 将注册表单数据转换为API请求格式
   * @param formData 注册表单数据
   * @returns API请求格式的数据
   */
  convertRegisterFormToApiRequest(formData: RegisterFormData) {
    return {
      username: formData.email || '',
      password: formData.password || '',
      alias: formData.alias || '',
      profile: {
        firstName: formData.firstName || '',
        lastName: formData.lastName || '',
        addressLine1: formData.addressLine1,
        addressLine2: formData.addressLine2,
        stateProvince: formData.stateProvince || formData.state,
        countryCode: formData.countryCode,
        postalZipCode: formData.postalZipCode,
        avatarUrl: formData.avatarUrl,
        stageName: formData.stageName,
        bio: formData.bio,
      },
      defaultRoleId: formData.defaultRoleId || 'account.role.investor',
    };
  },

  /**
   * 将登录表单数据转换为API请求格式
   * @param formData 登录表单数据
   * @returns API请求格式的数据
   */
  convertLoginFormToApiRequest(formData: LoginFormData) {
    return {
      username: formData.email,
      password: formData.password,
    };
  },

  /**
   * 将用户资料响应转换为用户对象
   * @param profileResponse 用户资料响应
   * @returns 用户对象
   */
  convertProfileResponseToUser(profileResponse: UserProfileResponse) {
    return {
      accountId: profileResponse.accountId || '',
      email: profileResponse.email,
      alias: profileResponse.alias,
      firstName: profileResponse.firstName,
      lastName: profileResponse.lastName,
      mobile: profileResponse.mobile,
      addressLine1: profileResponse.addressLine1,
      addressLine2: profileResponse.addressLine2,
      stateProvince: profileResponse.stateProvince,
      countryCode: profileResponse.countryCode,
      postalZipCode: profileResponse.postalZipCode,
      avatarUrl: profileResponse.avatarUrl,
      stageName: profileResponse.stageName,
      bio: profileResponse.bio,
      displayName: profileResponse.displayName,
      roles: [], // 需要从其他接口获取
    };
  },
};

// 保持向后兼容的旧API
export const user = {
  register: (_params: RegisterFormData) => {
    // 这里可以调用新的认证API，但为了保持兼容性暂时保留
    console.warn(
      'user.register is deprecated, please use authApi.signup instead'
    );
    return userApi.getProfile(); // 临时实现
  },
  login: (_params: LoginFormData) => {
    // 这里可以调用新的认证API，但为了保持兼容性暂时保留
    console.warn('user.login is deprecated, please use authApi.login instead');
    return userApi.getProfile(); // 临时实现
  },
  getUserInfo: () => {
    console.warn(
      'user.getUserInfo is deprecated, please use userApi.getProfile instead'
    );
    return userApi.getProfile();
  },
};

// 导出默认对象
export default userApi;
