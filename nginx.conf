# 将 music.renee-arts.com 的 HTTP 请求重定向到 HTTPS
server {
    listen 80;
    server_name music.renee-arts.com;
    return 301 https://$server_name$request_uri;
}

# music.renee-arts.com 的主服务器块 (HTTPS)
server {
    listen 443 ssl http2;
    server_name music.renee-arts.com;

    # SSL 证书由 Certbot 管理
    ssl_certificate /etc/letsencrypt/live/music.renee-arts.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/music.renee-arts.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    # React 应用的根目录
    root /mnt/data/web/sites/music.renee-arts.com/www;
    index index.html;

    # 为所有响应添加 CORS 头
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Authorization, Content-Type" always;
    add_header Access-Control-Allow-Credentials "true" always;

    # 处理预检 OPTIONS 请求
    if ($request_method = OPTIONS) {
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Authorization, Content-Type" always;
        add_header Access-Control-Allow-Credentials "true" always;
        add_header Access-Control-Max-Age "1728000";
        add_header Content-Type "text/plain charset=UTF-8";
        add_header Content-Length 0;
        return 204;
    }

    # 使用缓存提供静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # 为所有非静态路由提供 index.html 以支持 React Router 的 history 模式
    location / {
        try_files $uri $uri/ /index.html;
    }
}

# API 服务器块 (musicapi.renee-arts.com)
server {
    listen 80;
    server_name musicapi.renee-arts.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name musicapi.renee-arts.com;

    # SSL 证书由 Certbot 管理
    ssl_certificate /etc/letsencrypt/live/music.renee-arts.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/music.renee-arts.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    # 将请求代理到后端服务
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 为 API 响应添加 CORS 头
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Authorization, Content-Type" always;
        add_header Access-Control-Allow-Credentials "true" always;

        # 处理 API 的预检 OPTIONS 请求
        if ($request_method = OPTIONS) {
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Authorization, Content-Type" always;
            add_header Access-Control-Allow-Credentials "true" always;
            add_header Access-Control-Max-Age "1728000";
            add_header Content-Type "text/plain charset=UTF-8";
            add_header Content-Length 0;
            return 204;
        }
    }
}